-- 初始化数据
-- 创建时间: 2025-07-30

-- 创建默认超级管理员
-- 用户名: root, 密码: password (使用bcrypt加密)
INSERT INTO admins (id, username, password_hash, role, created_by, authorized_products, status)
VALUES (
  'admin_001',
  'root',
  '$2b$10$3r2CGInQEFZZvGLDhaJelu29//jkBCkYF.cSdEINQuocowRCp0VSK',
  'super',
  NULL,
  NULL,
  'active'
);

-- 创建示例产品
INSERT INTO products (id, name, description, price, download_url, verification_strategies, created_by)
VALUES (
  'prod_001',
  'Demo Software v1.0',
  '演示软件，用于测试许可证验证功能',
  99.99,
  'https://example.com/downloads/demo-software-v1.0.zip',
  '{"expiry": true, "device_limit": 3, "features": ["basic", "advanced"]}',
  'admin_001'
);

-- 创建第二个示例产品
INSERT INTO products (id, name, description, price, download_url, verification_strategies, created_by)
VALUES (
  'prod_002',
  'Professional Software v2.0',
  '专业版软件，包含高级功能',
  199.99,
  'https://example.com/downloads/professional-software-v2.0.zip',
  '{"expiry": true, "device_limit": 5, "features": ["basic", "advanced", "premium"]}',
  'admin_001'
);

-- 创建示例许可证
INSERT INTO licenses (id, license_key, product_id, admin_id, price, expiry_date, device_limit, features)
VALUES (
  'lic_001',
  'DEMO-ABCD-EFGH-IJKL',
  'prod_001',
  'admin_001',
  99.99,
  '2024-12-31 23:59:59',
  3,
  '["basic", "advanced"]'
);

-- 创建第二个示例许可证（永久许可证）
INSERT INTO licenses (id, license_key, product_id, admin_id, price, expiry_date, device_limit, features)
VALUES (
  'lic_002',
  'DEMO-MNOP-QRST-UVWX',
  'prod_002',
  'admin_001',
  199.99,
  NULL,
  5,
  '["basic", "advanced", "premium"]'
);

-- 创建示例订单
INSERT INTO orders (id, order_number, license_id, admin_id, channel, customer_name, remarks, amount, status, sold_at)
VALUES (
  'ord_001',
  'ORD-20250730-001',
  'lic_001',
  'admin_001',
  '官网直销',
  '张三',
  '企业客户，需要发票',
  99.99,
  'sold',
  '2025-07-30 10:30:00'
);

-- 创建第二个示例订单
INSERT INTO orders (id, order_number, license_id, admin_id, channel, customer_name, remarks, amount, status, sold_at)
VALUES (
  'ord_002',
  'ORD-20250730-002',
  'lic_002',
  'admin_001',
  '代理商销售',
  '李四',
  '个人用户',
  199.99,
  'sold',
  '2025-07-30 11:00:00'
);

-- 创建示例普通管理员（分销商）
-- 用户名: user, 密码: password (使用bcrypt加密)
INSERT INTO admins (id, username, password_hash, role, created_by, authorized_products, status)
VALUES (
  'admin_002',
  'user',
  '$2b$10$TuxDyEkVb15jAelwjd2ya.PxWyStllqFd0DUWcKD.4kz9ddnKdunW',
  'normal',
  'admin_001',
  '["prod_001", "prod_002"]',
  'active'
);
