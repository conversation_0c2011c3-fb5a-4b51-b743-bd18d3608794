// 类型定义文件

// 环境变量类型
export interface Env {
  DB: D1Database;
  VERIFY_CACHE: KVNamespace;
  JWT_SECRET?: string;
}

// 通用响应格式
export interface ApiResponse<T = any> {
  success: boolean;
  data: T | null;
  msg: string;
}

// 管理员相关类型
export interface Admin {
  id: string;
  username: string;
  password_hash: string;
  role: 'super' | 'normal';
  created_by?: string;
  authorized_products?: string; // JSON数组字符串
  status: 'active' | 'disabled';
  created_at: string;
  updated_at: string;
  last_login?: string;
}

export interface AdminLoginRequest {
  username: string;
  password: string;
}

export interface AdminLoginResponse {
  token: string;
  admin_id: string;
  username: string;
  role: 'super' | 'normal';
  authorized_products?: string[];
  expires_in: number;
}

// 产品相关类型
export interface Product {
  id: string;
  name: string;
  description?: string;
  price: number;
  download_url?: string;
  verification_strategies: string; // JSON字符串
  status: 'active' | 'disabled';
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface VerificationStrategies {
  expiry: boolean;
  device_limit: number;
  features: string[];
}

// 许可证相关类型
export interface License {
  id: string;
  license_key: string;
  product_id: string;
  admin_id: string;
  price: number;
  expiry_date?: string;
  device_limit: number;
  features?: string; // JSON数组字符串
  status: 'active' | 'revoked' | 'expired';
  metadata?: string; // JSON字符串
  created_at: string;
  updated_at: string;
  activated_at?: string;
  last_verified?: string;
}

// 验证相关类型
export interface VerifyRequest {
  license_key: string;
  device_fingerprint?: string;
  requested_features?: string[];
  client_info?: {
    version?: string;
    platform?: string;
    ip?: string;
  };
}

export interface VerifyResponse {
  license_id: string;
  product_id: string;
  product_name: string;
  status: string;
  expiry_date?: string;
  days_remaining?: number;
  device_limit: number;
  current_devices: number;
  device_slots_available: number;
  features: {
    granted: string[];
    denied: string[];
    available: string[];
  };
  device_info?: {
    is_new_device: boolean;
    device_id?: string;
    first_seen?: string;
    last_seen?: string;
  };
  verification_time: string;
}

// JWT Token 载荷类型
export interface JWTPayload {
  admin_id: string;
  username: string;
  role: 'super' | 'normal';
  authorized_products?: string[];
  iat: number;
  exp: number;
}

// 分页相关类型
export interface PaginationParams {
  page?: number;
  limit?: number;
}

export interface PaginationResponse {
  current_page: number;
  total_pages: number;
  total_count: number;
  per_page: number;
}
