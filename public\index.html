<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件许可证验证服务</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .api-info {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        
        .api-info h2 {
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .api-info p {
            margin-bottom: 0.5rem;
        }
        
        .api-link {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 0.75rem 1.5rem;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .api-link:hover {
            background: #0056b3;
        }
        
        .features {
            list-style: none;
            margin-top: 1rem;
        }
        
        .features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        
        .features li:last-child {
            border-bottom: none;
        }
        
        .features li::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>软件许可证验证服务</h1>
        
        <div class="api-info">
            <h2>API 服务</h2>
            <p>基于 Cloudflare Workers 的高性能许可证验证服务</p>
            
            <ul class="features">
                <li>许可证生成与验证</li>
                <li>实时状态检查</li>
                <li>安全加密传输</li>
                <li>全球边缘节点部署</li>
                <li>RESTful API 接口</li>
            </ul>
        </div>
        
        <div style="text-align: center;">
            <a href="/api/docs" class="api-link">查看 API 文档</a>
        </div>
    </div>
</body>
</html>
