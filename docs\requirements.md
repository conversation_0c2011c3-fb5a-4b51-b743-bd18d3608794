# Verify - 软件许可证验证服务系统

一个面向个人开发者的轻量级软件许可证网络验证服务系统，基于 Cloudflare Workers 环境构建。

## 1. 系统概述

### 目标用户

- **主要用户**：个人软件开发者
- **次要用户**：软件分销商/代理商

### 核心价值

- 提供可靠的在线许可证验证服务
- 支持灵活的验证策略组合
- 简化许可证分发和管理流程
- 提供清晰的销售统计功能

### 技术基础

- 基于 Cloudflare Workers 无服务器架构
- 全球边缘节点部署，低延迟响应
- 高可用性和自动扩展

## 2. 核心功能需求

### 2.1 许可证验证服务

#### 验证策略支持

系统支持以下验证策略，可单独使用或灵活组合：

**过期时间验证**

- 检查许可证是否在有效期内
- 支持永久许可证（无过期时间）
- 返回剩余有效时间信息

**设备数限制验证**

- 限制同一许可证可绑定的设备数量
- 基于设备指纹进行设备识别
- 支持设备解绑和重新绑定
- 防止许可证滥用和共享

**功能模块验证**

- 控制不同功能模块的访问权限
- 支持功能级别的许可证授权
- 灵活的功能组合配置
- 支持功能升级和降级

#### 验证接口

- 客户端提供许可证密钥进行验证
- 可选提供设备指纹和请求功能列表
- 返回验证结果、过期时间、允许功能等信息

### 2.2 分级管理员体系

#### 超级管理员（软件开发者）

**权限范围**

- 创建和管理软件产品
- 配置产品的验证策略
- 创建和管理普通管理员账号
- 生成和撤销所有许可证
- 查看全局销售统计数据
- 系统配置和安全设置

**核心功能**

- 产品管理：创建产品、设置价格、配置验证策略
- 分销商管理：创建分销商账号、分配产品权限
- 全局统计：查看所有产品的销售情况和分销商业绩

#### 普通管理员（分销商）

**权限范围**

- 为授权产品生成许可证
- 撤销自己生成的许可证
- 查看自己的销售统计
- 管理自己分发的许可证状态

**权限限制**

- 无法修改产品配置
- 无法查看其他分销商数据
- 无法创建其他管理员账号
- 只能操作被授权的产品

### 2.3 销售统计 Dashboard

#### 超级管理员统计视图

**产品销售概览**

- 各产品的总销售额和销售数量
- 销售趋势图表（按时间维度）
- 热门产品排行

**分销商业绩统计**

- 各分销商的销售业绩排行
- 分销商销售明细
- 分销商活跃度统计

**许可证使用分析**

- 许可证激活率统计
- 设备绑定情况分析
- 功能使用情况统计

#### 普通管理员统计视图

**个人销售统计**

- 自己的总销售额和数量
- 按产品分类的销售统计
- 销售趋势分析

**许可证管理**

- 生成的许可证列表
- 许可证状态监控
- 客户使用情况跟踪

## 3. 数据模型

### 核心实体

- **Product（软件产品）**：产品信息、价格、验证策略配置
- **Admin（管理员）**：管理员账号、角色、权限配置
- **License（许可证）**：许可证密钥、关联产品、过期时间、设备限制、功能权限
- **Device（设备绑定）**：许可证与设备的绑定关系
- **VerificationLog（验证日志）**：验证请求的详细记录

### 关系设计

- 一个产品可以有多个许可证
- 一个管理员可以生成多个许可证
- 一个许可证可以绑定多个设备（受限制）
- 一个许可证有多次验证记录

## 4. API 接口需求

### 4.1 验证接口（客户端调用）

- 许可证验证接口：验证许可证有效性
- 支持设备指纹验证和功能权限检查
- 返回详细的验证结果和权限信息

### 4.2 管理接口（管理员调用）

- 管理员认证：登录获取访问令牌
- 产品管理：创建、查看、更新、删除产品（仅超级管理员）
- 许可证管理：生成、查看、撤销许可证
- 统计查询：获取销售统计和使用分析数据
- 分销商管理：创建和管理分销商账号（仅超级管理员）

## 5. 技术架构需求

### 5.1 存储需求

- **主数据库**：存储核心业务数据（管理员、产品、许可证等）
- **缓存层**：高频验证请求缓存，提升响应速度
- **日志存储**：验证日志和操作审计记录

### 5.2 安全需求

- **认证机制**：JWT Token 认证管理员身份
- **权限控制**：基于角色的访问控制
- **防护措施**：请求频率限制、设备指纹验证、数据加密
- **审计日志**：完整的操作记录和验证日志

### 5.3 性能需求

- **响应时间**：验证接口响应时间 < 200ms
- **并发支持**：支持 1000+并发验证请求
- **缓存策略**：验证结果缓存、配置缓存
- **全球部署**：利用边缘节点降低延迟

### 5.4 监控需求

- **性能监控**：API 响应时间、成功率、错误率
- **业务监控**：许可证使用情况、销售统计
- **安全监控**：异常访问、频繁验证、可疑行为

