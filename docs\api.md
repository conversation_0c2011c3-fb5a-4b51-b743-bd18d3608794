# API 接口设计文档

基于 Cloudflare Workers 的软件许可证验证服务系统 API 设计，提供许可证验证和管理功能。

## 1. 接口概述

### 1.1 基础信息

- **Base URL**: `https://verify.your-domain.com/api`
- **协议**: HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 统一响应格式

所有 API 接口统一使用以下响应格式：

```json
{
  "success": true, // 请求是否成功
  "data": {}, // 响应数据，失败时为null
  "msg": "操作成功" // 响应消息
}
```

### 1.3 HTTP 状态码

- `200` - 请求成功
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `429` - 请求频率限制
- `500` - 服务器内部错误

### 1.4 错误响应示例

```json
{
  "success": false,
  "data": null,
  "msg": "许可证不存在或已过期"
}
```

## 2. 验证接口（客户端调用）

### 2.1 许可证验证

**接口地址**: `POST /verify`

**功能描述**: 验证许可证的有效性，支持过期时间、设备限制、功能权限等多种验证策略。

**请求参数**:

```json
{
  "license_key": "DEMO-XXXX-XXXX-XXXX", // 必填：许可证密钥
  "device_fingerprint": "device_001", // 可选：设备指纹
  "requested_features": ["basic", "advanced"], // 可选：请求的功能列表
  "client_info": {
    // 可选：客户端信息
    "version": "1.0.0",
    "platform": "Windows 10",
    "ip": "*************"
  }
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "license_id": "lic_123",
    "product_id": "prod_456",
    "product_name": "My Software v1.0",
    "status": "active",
    "expiry_date": "2024-12-31T23:59:59Z", // null表示永久许可证
    "days_remaining": 365, // 剩余天数，永久许可证为-1
    "device_limit": 3,
    "current_devices": 2,
    "device_slots_available": 1,
    "features": {
      "granted": ["basic", "advanced"], // 已授权功能
      "denied": [], // 被拒绝功能
      "available": ["basic", "advanced", "premium"] // 产品所有功能
    },
    "device_info": {
      "is_new_device": false, // 是否为新设备
      "device_id": "dev_789", // 设备ID
      "first_seen": "2024-01-01T00:00:00Z",
      "last_seen": "2024-01-15T10:30:00Z"
    },
    "verification_time": "2024-01-15T10:30:00Z"
  },
  "msg": "许可证验证成功"
}
```

**失败响应示例**:

```json
// 许可证不存在
{
  "success": false,
  "data": null,
  "msg": "许可证不存在"
}

// 许可证已过期
{
  "success": false,
  "data": {
    "license_id": "lic_123",
    "status": "expired",
    "expiry_date": "2023-12-31T23:59:59Z"
  },
  "msg": "许可证已过期"
}

// 设备数量超限
{
  "success": false,
  "data": {
    "license_id": "lic_123",
    "device_limit": 3,
    "current_devices": 3,
    "device_slots_available": 0
  },
  "msg": "设备数量已达上限"
}

// 功能权限不足
{
  "success": false,
  "data": {
    "license_id": "lic_123",
    "requested_features": ["premium"],
    "granted_features": ["basic", "advanced"],
    "denied_features": ["premium"]
  },
  "msg": "功能权限不足"
}
```

### 2.2 设备解绑

**接口地址**: `POST /device/unbind`

**功能描述**: 解绑指定设备，释放设备槽位。

**请求参数**:

```json
{
  "license_key": "DEMO-XXXX-XXXX-XXXX", // 必填：许可证密钥
  "device_fingerprint": "device_001" // 必填：要解绑的设备指纹
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "license_id": "lic_123",
    "device_limit": 3,
    "current_devices": 2,
    "device_slots_available": 1,
    "unbound_device": "device_001"
  },
  "msg": "设备解绑成功"
}
```

## 3. 管理接口（管理员调用）

### 3.1 认证相关

#### 3.1.1 管理员登录

**接口地址**: `POST /admin/login`

**功能描述**: 管理员登录获取访问令牌。

**请求参数**:

```json
{
  "username": "admin", // 必填：用户名
  "password": "password123" // 必填：密码
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "admin_id": "admin_123",
    "username": "admin",
    "role": "super", // super: 超级管理员, normal: 普通管理员
    "authorized_products": ["prod_123", "prod_456"], // 普通管理员的授权产品列表
    "expires_in": 86400 // Token有效期（秒）
  },
  "msg": "登录成功"
}
```

#### 3.1.2 Token 刷新

**接口地址**: `POST /admin/refresh`

**请求头**: `Authorization: Bearer <token>`

**成功响应**:

```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expires_in": 86400
  },
  "msg": "Token刷新成功"
}
```

### 3.2 产品管理（仅超级管理员）

#### 3.2.1 创建产品

**接口地址**: `POST /admin/products`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:

```json
{
  "name": "My Software v2.0", // 必填：产品名称
  "description": "新版本软件", // 可选：产品描述
  "price": 199.99, // 必填：产品价格
  "download_url": "https://example.com/downloads/v2.0.zip", // 可选：下载链接
  "verification_strategies": {
    // 必填：验证策略配置
    "expiry": true, // 是否启用过期验证
    "device_limit": 5, // 设备数量限制
    "features": ["basic", "advanced", "premium"] // 可用功能列表
  }
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "product_id": "prod_789",
    "name": "My Software v2.0",
    "description": "新版本软件",
    "price": 199.99,
    "download_url": "https://example.com/downloads/v2.0.zip",
    "verification_strategies": {
      "expiry": true,
      "device_limit": 5,
      "features": ["basic", "advanced", "premium"]
    },
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z"
  },
  "msg": "产品创建成功"
}
```

#### 3.2.2 获取产品列表

**接口地址**: `GET /admin/products`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:

- `page`: 页码（默认 1）
- `limit`: 每页数量（默认 20）
- `status`: 产品状态筛选（active/disabled）
- `search`: 产品名称搜索

**成功响应**:

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "product_id": "prod_123",
        "name": "My Software v1.0",
        "description": "专业软件解决方案",
        "price": 99.99,
        "status": "active",
        "license_count": 150, // 许可证总数
        "active_licenses": 120, // 活跃许可证数
        "total_revenue": 14999.85, // 总收入
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_count": 100,
      "per_page": 20
    }
  },
  "msg": "获取产品列表成功"
}
```

#### 3.2.3 更新产品

**接口地址**: `PUT /admin/products/{product_id}`

**请求头**: `Authorization: Bearer <token>`

**请求参数**: 同创建产品，所有字段可选

**成功响应**:

```json
{
  "success": true,
  "data": {
    "product_id": "prod_123",
    "name": "My Software v1.1",
    "price": 129.99,
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "msg": "产品更新成功"
}
```

#### 3.2.4 删除产品

**接口地址**: `DELETE /admin/products/{product_id}`

**请求头**: `Authorization: Bearer <token>`

**成功响应**:

```json
{
  "success": true,
  "data": {
    "product_id": "prod_123",
    "deleted_at": "2024-01-15T10:30:00Z"
  },
  "msg": "产品删除成功"
}
```

### 3.3 许可证管理

#### 3.3.1 生成许可证

**接口地址**: `POST /admin/licenses`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:

```json
{
  "product_id": "prod_123", // 必填：产品ID
  "price": 99.99, // 必填：销售价格
  "expiry_date": "2024-12-31T23:59:59Z", // 可选：过期时间，null为永久
  "device_limit": 3, // 可选：设备限制，默认使用产品配置
  "features": ["basic", "advanced"], // 可选：功能列表，默认使用产品配置
  "order_info": {
    // 可选：订单信息
    "channel": "官网直销",
    "customer_name": "张三",
    "remarks": "企业客户，需要发票"
  }
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "license_id": "lic_456",
    "license_key": "DEMO-ABCD-EFGH-IJKL",
    "product_id": "prod_123",
    "product_name": "My Software v1.0",
    "price": 99.99,
    "expiry_date": "2024-12-31T23:59:59Z",
    "device_limit": 3,
    "features": ["basic", "advanced"],
    "status": "active",
    "order_info": {
      "order_id": "ord_789",
      "order_number": "ORD-20240115-002",
      "channel": "官网直销",
      "customer_name": "张三",
      "remarks": "企业客户，需要发票"
    },
    "created_at": "2024-01-15T10:30:00Z"
  },
  "msg": "许可证生成成功"
}
```

#### 3.3.2 获取许可证列表

**接口地址**: `GET /admin/licenses`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:

- `page`: 页码（默认 1）
- `limit`: 每页数量（默认 20）
- `product_id`: 产品 ID 筛选
- `status`: 许可证状态筛选（active/revoked/expired）
- `search`: 许可证密钥搜索
- `admin_id`: 生成者 ID 筛选（超级管理员可用）

**成功响应**:

```json
{
  "success": true,
  "data": {
    "licenses": [
      {
        "license_id": "lic_123",
        "license_key": "DEMO-XXXX-XXXX-XXXX",
        "product_id": "prod_456",
        "product_name": "My Software v1.0",
        "admin_id": "admin_123",
        "admin_username": "admin",
        "price": 99.99,
        "status": "active",
        "expiry_date": "2024-12-31T23:59:59Z",
        "device_limit": 3,
        "current_devices": 2,
        "features": ["basic", "advanced"],
        "order_info": {
          "order_number": "ORD-20240115-001",
          "channel": "官网直销",
          "customer_name": "张三"
        },
        "created_at": "2024-01-01T00:00:00Z",
        "activated_at": "2024-01-02T10:30:00Z",
        "last_verified": "2024-01-15T09:45:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 10,
      "total_count": 200,
      "per_page": 20
    }
  },
  "msg": "获取许可证列表成功"
}
```

#### 3.3.3 撤销许可证

**接口地址**: `POST /admin/licenses/{license_id}/revoke`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:

```json
{
  "reason": "客户申请退款" // 可选：撤销原因
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "license_id": "lic_123",
    "license_key": "DEMO-XXXX-XXXX-XXXX",
    "status": "revoked",
    "revoked_at": "2024-01-15T10:30:00Z",
    "reason": "客户申请退款"
  },
  "msg": "许可证撤销成功"
}
```

#### 3.3.4 获取许可证详情

**接口地址**: `GET /admin/licenses/{license_id}`

**请求头**: `Authorization: Bearer <token>`

**成功响应**:

```json
{
  "success": true,
  "data": {
    "license_info": {
      "license_id": "lic_123",
      "license_key": "DEMO-XXXX-XXXX-XXXX",
      "product_id": "prod_456",
      "product_name": "My Software v1.0",
      "admin_id": "admin_123",
      "admin_username": "admin",
      "price": 99.99,
      "status": "active",
      "expiry_date": "2024-12-31T23:59:59Z",
      "device_limit": 3,
      "features": ["basic", "advanced"],
      "created_at": "2024-01-01T00:00:00Z",
      "activated_at": "2024-01-02T10:30:00Z",
      "last_verified": "2024-01-15T09:45:00Z"
    },
    "order_info": {
      "order_id": "ord_456",
      "order_number": "ORD-20240101-001",
      "channel": "官网直销",
      "customer_name": "张三",
      "remarks": "企业客户，需要发票",
      "amount": 99.99,
      "status": "sold",
      "sold_at": "2024-01-01T00:00:00Z"
    },
    "devices": [
      {
        "device_id": "dev_123",
        "device_fingerprint": "device_001",
        "device_info": {
          "platform": "Windows 10",
          "version": "1.0.0"
        },
        "status": "active",
        "first_seen": "2024-01-02T10:30:00Z",
        "last_seen": "2024-01-15T09:45:00Z"
      }
    ],
    "verification_stats": {
      "total_verifications": 150,
      "success_rate": 98.67,
      "last_7_days": 25,
      "last_30_days": 120
    }
  },
  "msg": "获取许可证详情成功"
}
```

### 3.4 管理员管理（仅超级管理员）

#### 3.4.1 创建管理员

**接口地址**: `POST /admin/admins`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:

```json
{
  "username": "distributor01", // 必填：用户名
  "password": "password123", // 必填：密码
  "role": "normal", // 必填：角色（normal）
  "authorized_products": ["prod_123", "prod_456"] // 必填：授权产品列表
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "admin_id": "admin_456",
    "username": "distributor01",
    "role": "normal",
    "authorized_products": ["prod_123", "prod_456"],
    "status": "active",
    "created_at": "2024-01-15T10:30:00Z"
  },
  "msg": "管理员创建成功"
}
```

#### 3.4.2 获取管理员列表

**接口地址**: `GET /admin/admins`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:

- `page`: 页码（默认 1）
- `limit`: 每页数量（默认 20）
- `role`: 角色筛选（super/normal）
- `status`: 状态筛选（active/disabled）

**成功响应**:

```json
{
  "success": true,
  "data": {
    "admins": [
      {
        "admin_id": "admin_123",
        "username": "distributor01",
        "role": "normal",
        "authorized_products": ["prod_123", "prod_456"],
        "status": "active",
        "stats": {
          "total_licenses": 50,
          "total_revenue": 4999.5,
          "last_30_days_licenses": 15,
          "last_30_days_revenue": 1499.85
        },
        "created_at": "2024-01-01T00:00:00Z",
        "last_login": "2024-01-15T09:30:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 3,
      "total_count": 50,
      "per_page": 20
    }
  },
  "msg": "获取管理员列表成功"
}
```

#### 3.4.3 更新管理员

**接口地址**: `PUT /admin/admins/{admin_id}`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:

```json
{
  "password": "newpassword123", // 可选：新密码
  "authorized_products": ["prod_123"], // 可选：授权产品列表
  "status": "disabled" // 可选：账号状态
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "admin_id": "admin_456",
    "username": "distributor01",
    "authorized_products": ["prod_123"],
    "status": "disabled",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "msg": "管理员更新成功"
}
```

### 3.5 统计数据

#### 3.5.1 获取销售统计

**接口地址**: `GET /admin/stats/sales`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:

- `period`: 统计周期（7d/30d/90d/1y，默认 30d）
- `product_id`: 产品 ID 筛选（可选）
- `admin_id`: 管理员 ID 筛选（超级管理员可用）

**成功响应**:

```json
{
  "success": true,
  "data": {
    "summary": {
      "total_revenue": 49999.5,
      "total_orders": 500,
      "sold_orders": 450,
      "refunded_orders": 10,
      "refunded_amount": 999.9,
      "period": "30d"
    },
    "products": [
      {
        "product_id": "prod_123",
        "product_name": "My Software v1.0",
        "revenue": 29999.7,
        "orders_count": 300,
        "sold_count": 270,
        "refunded_count": 5,
        "avg_price": 99.99
      }
    ],
    "daily_stats": [
      {
        "date": "2024-01-15",
        "revenue": 1999.8,
        "orders": 20,
        "sold": 18,
        "refunded": 0
      }
    ],
    "top_distributors": [
      {
        "admin_id": "admin_456",
        "username": "distributor01",
        "revenue": 9999.9,
        "orders": 100,
        "conversion_rate": 90.0
      }
    ]
  },
  "msg": "获取销售统计成功"
}
```

#### 3.5.2 获取许可证使用统计

**接口地址**: `GET /admin/stats/usage`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:

- `period`: 统计周期（7d/30d/90d/1y，默认 30d）
- `product_id`: 产品 ID 筛选（可选）

**成功响应**:

```json
{
  "success": true,
  "data": {
    "summary": {
      "total_licenses": 1000,
      "active_licenses": 850,
      "expired_licenses": 100,
      "revoked_licenses": 50,
      "activation_rate": 85.0,
      "period": "30d"
    },
    "verification_stats": {
      "total_verifications": 50000,
      "success_rate": 98.5,
      "avg_daily_verifications": 1667,
      "peak_daily_verifications": 3500
    },
    "device_stats": {
      "total_devices": 2000,
      "avg_devices_per_license": 2.35,
      "device_utilization_rate": 78.3
    },
    "feature_usage": [
      {
        "feature": "basic",
        "usage_count": 850,
        "usage_rate": 100.0
      },
      {
        "feature": "advanced",
        "usage_count": 680,
        "usage_rate": 80.0
      },
      {
        "feature": "premium",
        "usage_count": 340,
        "usage_rate": 40.0
      }
    ]
  },
  "msg": "获取使用统计成功"
}
```

### 3.6 订单管理

#### 3.6.1 获取订单列表

**接口地址**: `GET /admin/orders`

**请求头**: `Authorization: Bearer <token>`

**查询参数**:

- `page`: 页码（默认 1）
- `limit`: 每页数量（默认 20）
- `status`: 订单状态筛选（generated/sold/refunded）
- `product_id`: 产品 ID 筛选
- `admin_id`: 销售员 ID 筛选（超级管理员可用）
- `channel`: 销售渠道筛选
- `customer_name`: 客户名称搜索
- `start_date`: 开始日期
- `end_date`: 结束日期

**成功响应**:

```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "order_id": "ord_123",
        "order_number": "ORD-20240115-001",
        "license_id": "lic_456",
        "license_key": "DEMO-XXXX-XXXX-XXXX",
        "product_id": "prod_789",
        "product_name": "My Software v1.0",
        "admin_id": "admin_123",
        "admin_username": "distributor01",
        "channel": "官网直销",
        "customer_name": "张三",
        "remarks": "企业客户，需要发票",
        "amount": 99.99,
        "status": "sold",
        "sold_at": "2024-01-15T10:30:00Z",
        "refund_reason": null,
        "created_at": "2024-01-15T10:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 25,
      "total_count": 500,
      "per_page": 20
    },
    "summary": {
      "total_amount": 49999.5,
      "sold_amount": 44999.55,
      "refunded_amount": 999.9,
      "generated_count": 50,
      "sold_count": 450,
      "refunded_count": 10
    }
  },
  "msg": "获取订单列表成功"
}
```

#### 3.6.2 更新订单状态

**接口地址**: `PUT /admin/orders/{order_id}/status`

**请求头**: `Authorization: Bearer <token>`

**请求参数**:

```json
{
  "status": "sold", // 必填：新状态（sold/refunded）
  "refund_reason": "客户申请退款" // 退款时必填
}
```

**成功响应**:

```json
{
  "success": true,
  "data": {
    "order_id": "ord_123",
    "order_number": "ORD-20240115-001",
    "status": "sold",
    "sold_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  "msg": "订单状态更新成功"
}
```

#### 3.6.3 获取订单详情

**接口地址**: `GET /admin/orders/{order_id}`

**请求头**: `Authorization: Bearer <token>`

**成功响应**:

```json
{
  "success": true,
  "data": {
    "order_info": {
      "order_id": "ord_123",
      "order_number": "ORD-20240115-001",
      "license_id": "lic_456",
      "license_key": "DEMO-XXXX-XXXX-XXXX",
      "product_id": "prod_789",
      "product_name": "My Software v1.0",
      "admin_id": "admin_123",
      "admin_username": "distributor01",
      "channel": "官网直销",
      "customer_name": "张三",
      "remarks": "企业客户，需要发票",
      "amount": 99.99,
      "status": "sold",
      "sold_at": "2024-01-15T10:30:00Z",
      "created_at": "2024-01-15T10:00:00Z"
    },
    "license_info": {
      "status": "active",
      "expiry_date": "2024-12-31T23:59:59Z",
      "device_limit": 3,
      "current_devices": 2,
      "features": ["basic", "advanced"],
      "activated_at": "2024-01-15T11:00:00Z",
      "last_verified": "2024-01-15T15:30:00Z"
    }
  },
  "msg": "获取订单详情成功"
}
```

## 4. 错误码定义

### 4.1 通用错误码

| 错误码 | HTTP 状态码 | 错误信息       | 说明                   |
| ------ | ----------- | -------------- | ---------------------- |
| 10001  | 400         | 请求参数错误   | 必填参数缺失或格式错误 |
| 10002  | 401         | 未授权访问     | Token 无效或已过期     |
| 10003  | 403         | 权限不足       | 无权限访问该资源       |
| 10004  | 404         | 资源不存在     | 请求的资源不存在       |
| 10005  | 429         | 请求频率限制   | 请求过于频繁           |
| 10006  | 500         | 服务器内部错误 | 系统内部错误           |

### 4.2 验证相关错误码

| 错误码 | HTTP 状态码 | 错误信息     | 说明                 |
| ------ | ----------- | ------------ | -------------------- |
| 20001  | 400         | 许可证不存在 | 许可证密钥无效       |
| 20002  | 400         | 许可证已过期 | 许可证超过有效期     |
| 20003  | 400         | 许可证已撤销 | 许可证被管理员撤销   |
| 20004  | 400         | 设备数量超限 | 绑定设备数量达到上限 |
| 20005  | 400         | 功能权限不足 | 请求的功能未授权     |
| 20006  | 400         | 设备指纹无效 | 设备指纹格式错误     |

### 4.3 管理相关错误码

| 错误码 | HTTP 状态码 | 错误信息         | 说明                     |
| ------ | ----------- | ---------------- | ------------------------ |
| 30001  | 400         | 用户名或密码错误 | 登录凭据无效             |
| 30002  | 400         | 用户名已存在     | 创建管理员时用户名重复   |
| 30003  | 403         | 无权限操作该产品 | 普通管理员操作未授权产品 |
| 30004  | 400         | 产品不存在       | 产品 ID 无效             |
| 30005  | 400         | 许可证不存在     | 许可证 ID 无效           |
| 30006  | 400         | 订单不存在       | 订单 ID 无效             |

## 5. 安全机制

### 5.1 认证机制

- **JWT Token**: 使用 JWT 进行身份认证
- **Token 有效期**: 24 小时，支持刷新
- **权限验证**: 基于角色的访问控制

### 5.2 请求限制

- **验证接口**: 每分钟最多 60 次请求
- **管理接口**: 每分钟最多 120 次请求
- **登录接口**: 每分钟最多 10 次请求

### 5.3 数据安全

- **HTTPS 传输**: 所有接口强制使用 HTTPS
- **参数验证**: 严格的输入参数验证
- **SQL 注入防护**: 使用参数化查询
- **XSS 防护**: 输出数据转义处理

## 6. 接口调用示例

### 6.1 客户端验证示例

```javascript
// 许可证验证
const verifyLicense = async (licenseKey, deviceFingerprint) => {
  try {
    const response = await fetch("https://verify.your-domain.com/api/verify", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        license_key: licenseKey,
        device_fingerprint: deviceFingerprint,
        requested_features: ["basic", "advanced"],
      }),
    });

    const result = await response.json();

    if (result.success) {
      console.log("验证成功:", result.data);
      return result.data;
    } else {
      console.error("验证失败:", result.msg);
      return null;
    }
  } catch (error) {
    console.error("网络错误:", error);
    return null;
  }
};
```

### 6.2 管理端调用示例

```javascript
// 管理员登录
const adminLogin = async (username, password) => {
  const response = await fetch(
    "https://verify.your-domain.com/api/admin/login",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ username, password }),
    }
  );

  const result = await response.json();
  if (result.success) {
    localStorage.setItem("admin_token", result.data.token);
    return result.data;
  }
  throw new Error(result.msg);
};

// 生成许可证
const generateLicense = async (productId, price, expiryDate) => {
  const token = localStorage.getItem("admin_token");

  const response = await fetch(
    "https://verify.your-domain.com/api/admin/licenses",
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({
        product_id: productId,
        price: price,
        expiry_date: expiryDate,
        order_info: {
          channel: "官网直销",
          customer_name: "客户姓名",
        },
      }),
    }
  );

  const result = await response.json();
  if (result.success) {
    return result.data;
  }
  throw new Error(result.msg);
};
```
