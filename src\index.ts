import { OpenAPIHono } from '@hono/zod-openapi'
import { swaggerUI } from '@hono/swagger-ui'
import { z } from 'zod'

// 定义环境类型
interface Env {
  ASSETS: Fetcher
  DB: D1Database
  VERIFY_CACHE: KVNamespace
}

// 创建应用实例
const app = new OpenAPIHono<{ Bindings: Env }>()

// 添加OpenAPI信息
app.openapi.addServer({
  url: 'https://license-verify-api.your-domain.workers.dev',
  description: '生产环境'
})

app.openapi.info = {
  title: '软件许可证验证服务 API',
  version: '1.0.0',
  description: '基于 Cloudflare Workers 的软件许可证验证服务，提供许可证生成、验证和管理功能。'
}

// 健康检查端点
app.openapi({
  method: 'get',
  path: '/api/health',
  summary: '健康检查',
  description: '检查服务状态',
  responses: {
    200: {
      description: '服务正常',
      content: {
        'application/json': {
          schema: z.object({
            status: z.string(),
            timestamp: z.string(),
            version: z.string()
          })
        }
      }
    }
  }
}, (c) => {
  return c.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// 许可证验证端点
app.openapi({
  method: 'post',
  path: '/api/verify',
  summary: '验证许可证',
  description: '验证软件许可证的有效性',
  requestBody: {
    content: {
      'application/json': {
        schema: z.object({
          license_key: z.string().min(1, '许可证密钥不能为空'),
          product_id: z.string().min(1, '产品ID不能为空'),
          machine_id: z.string().optional()
        })
      }
    }
  },
  responses: {
    200: {
      description: '验证成功',
      content: {
        'application/json': {
          schema: z.object({
            valid: z.boolean(),
            license_key: z.string(),
            product_id: z.string(),
            expires_at: z.string().optional(),
            features: z.array(z.string()).optional()
          })
        }
      }
    },
    400: {
      description: '请求参数错误',
      content: {
        'application/json': {
          schema: z.object({
            error: z.string(),
            message: z.string()
          })
        }
      }
    }
  }
}, async (c) => {
  const { license_key, product_id, machine_id } = c.req.valid('json')

  // TODO: 实现许可证验证逻辑
  // 这里先返回模拟数据
  return c.json({
    valid: true,
    license_key,
    product_id,
    expires_at: '2025-12-31T23:59:59Z',
    features: ['basic', 'premium']
  })
})

// Swagger UI 文档
app.get('/api/docs', swaggerUI({ url: '/api/openapi.json' }))

// OpenAPI JSON 规范
app.get('/api/openapi.json', (c) => {
  return c.json(app.openapi.getOpenAPIDocument())
})

// 静态文件处理 - 必须放在最后
app.get('*', async (c) => {
  return c.env.ASSETS.fetch(c.req.raw)
})

export default app