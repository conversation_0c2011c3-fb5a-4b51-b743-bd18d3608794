{"name": "license-verify-api", "version": "0.0.1", "private": true, "packageManager": "pnpm@9.0.0", "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "start": "wrangler dev", "cf-typegen": "wrangler types"}, "dependencies": {"@hono/swagger-ui": "^0.5.2", "@hono/zod-openapi": "^1.0.2", "hono": "^4.6.20", "nanoid": "^5.1.5", "zod": "^4.0.14"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241218.0", "@types/node": "22.13.0", "typescript": "^5.7.2", "wrangler": "^4.26.1"}}